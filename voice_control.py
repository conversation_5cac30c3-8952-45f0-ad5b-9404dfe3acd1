#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Приложение голосового управления для Windows 10
Использует speech_recognition для распознавания речи,
pyttsx3 для синтеза речи и subprocess для выполнения системных команд.
"""

import speech_recognition as sr
import pyttsx3
import subprocess
import os
import webbrowser
import time
import threading
from datetime import datetime


class VoiceController:
    def __init__(self):
        # Инициализация распознавания речи
        self.recognizer = sr.Recognizer()
        self.microphone = sr.Microphone()
        
        # Инициализация синтеза речи
        self.tts_engine = pyttsx3.init()
        self.setup_tts()
        
        # Настройка микрофона
        self.setup_microphone()
        
        # Флаг для остановки приложения
        self.running = True
        
        print("Голосовой помощник инициализирован!")
        self.speak("Голосовой помощник готов к работе!")

    def setup_tts(self):
        """Настройка параметров синтеза речи"""
        voices = self.tts_engine.getProperty('voices')
        
        # Попытка найти русский голос
        for voice in voices:
            if 'russian' in voice.name.lower() or 'ru' in voice.id.lower():
                self.tts_engine.setProperty('voice', voice.id)
                break
        
        # Настройка скорости и громкости
        self.tts_engine.setProperty('rate', 200)  # Скорость речи
        self.tts_engine.setProperty('volume', 0.9)  # Громкость

    def setup_microphone(self):
        """Настройка микрофона и калибровка шума"""
        print("Калибровка микрофона... Пожалуйста, молчите.")
        with self.microphone as source:
            self.recognizer.adjust_for_ambient_noise(source, duration=2)
        print("Калибровка завершена!")

    def speak(self, text):
        """Произнести текст"""
        print(f"Ассистент: {text}")
        self.tts_engine.say(text)
        self.tts_engine.runAndWait()

    def listen(self):
        """Слушать команду с микрофона"""
        try:
            with self.microphone as source:
                print("Слушаю...")
                # Увеличиваем timeout для лучшего распознавания
                audio = self.recognizer.listen(source, timeout=5, phrase_time_limit=5)
            
            print("Распознаю речь...")
            # Попытка распознать на русском языке
            command = self.recognizer.recognize_google(audio, language='ru-RU')
            print(f"Вы сказали: {command}")
            return command.lower()
            
        except sr.WaitTimeoutError:
            print("Время ожидания истекло")
            return None
        except sr.UnknownValueError:
            print("Не удалось распознать речь")
            self.speak("Извините, я не расслышал. Повторите, пожалуйста.")
            return None
        except sr.RequestError as e:
            print(f"Ошибка сервиса распознавания: {e}")
            self.speak("Ошибка подключения к сервису распознавания речи")
            return None

    def execute_command(self, command):
        """Выполнить голосовую команду"""
        if not command:
            return

        # Команды браузера
        if any(word in command for word in ['браузер', 'интернет', 'гугл', 'google']):
            self.speak("Открываю браузер")
            webbrowser.open('https://www.google.com')
        
        # Команды YouTube
        elif any(word in command for word in ['ютуб', 'youtube', 'видео']):
            self.speak("Открываю Ютуб")
            webbrowser.open('https://www.youtube.com')
        
        # Команды музыки
        elif any(word in command for word in ['музыка', 'плеер', 'spotify']):
            self.speak("Открываю музыкальный плеер")
            try:
                # Попытка открыть Spotify
                subprocess.run(['spotify'], check=True)
            except (subprocess.CalledProcessError, FileNotFoundError):
                # Если Spotify не найден, открываем веб-версию
                webbrowser.open('https://open.spotify.com')
        
        # Команды калькулятора
        elif any(word in command for word in ['калькулятор', 'посчитай']):
            self.speak("Открываю калькулятор")
            subprocess.run(['calc'])
        
        # Команды блокнота
        elif any(word in command for word in ['блокнот', 'notepad', 'записать']):
            self.speak("Открываю блокнот")
            subprocess.run(['notepad'])
        
        # Команды проводника
        elif any(word in command for word in ['проводник', 'папки', 'файлы']):
            self.speak("Открываю проводник")
            subprocess.run(['explorer'])
        
        # Команды времени
        elif any(word in command for word in ['время', 'час', 'сколько времени']):
            current_time = datetime.now().strftime("%H:%M")
            self.speak(f"Сейчас {current_time}")
        
        # Команды выключения
        elif any(word in command for word in ['выключи', 'выключение', 'shutdown']):
            self.speak("Выключаю компьютер через 30 секунд. Скажите 'отмена' для отмены.")
            subprocess.run(['shutdown', '/s', '/t', '30'])
        
        # Команды перезагрузки
        elif any(word in command for word in ['перезагрузка', 'restart', 'reboot']):
            self.speak("Перезагружаю компьютер через 30 секунд. Скажите 'отмена' для отмены.")
            subprocess.run(['shutdown', '/r', '/t', '30'])
        
        # Отмена выключения/перезагрузки
        elif any(word in command for word in ['отмена', 'cancel', 'стоп']):
            self.speak("Отменяю операцию")
            subprocess.run(['shutdown', '/a'])
        
        # Команды громкости
        elif any(word in command for word in ['громкость', 'звук']):
            if 'больше' in command or 'выше' in command:
                self.speak("Увеличиваю громкость")
                # Используем nircmd для управления громкостью (если установлен)
                try:
                    subprocess.run(['nircmd', 'changesysvolume', '5000'])
                except FileNotFoundError:
                    self.speak("Для управления громкостью нужна утилита nircmd")
            elif 'меньше' in command or 'тише' in command:
                self.speak("Уменьшаю громкость")
                try:
                    subprocess.run(['nircmd', 'changesysvolume', '-5000'])
                except FileNotFoundError:
                    self.speak("Для управления громкостью нужна утилита nircmd")
        
        # Команды завершения работы
        elif any(word in command for word in ['выход', 'закрыть', 'завершить', 'пока']):
            self.speak("До свидания!")
            self.running = False
        
        # Команды помощи
        elif any(word in command for word in ['помощь', 'команды', 'что умеешь']):
            help_text = """Я умею выполнять следующие команды:
            Открыть браузер, ютуб, музыку, калькулятор, блокнот, проводник.
            Сказать время. Выключить или перезагрузить компьютер.
            Управлять громкостью. Для выхода скажите 'выход'."""
            self.speak(help_text)
        
        else:
            self.speak("Извините, я не понимаю эту команду. Скажите 'помощь' для списка команд.")

    def run(self):
        """Основной цикл работы приложения"""
        self.speak("Скажите команду или 'помощь' для списка доступных команд")
        
        while self.running:
            try:
                command = self.listen()
                if command:
                    self.execute_command(command)
                time.sleep(0.5)  # Небольшая пауза между командами
                
            except KeyboardInterrupt:
                print("\nПрерывание работы...")
                self.speak("Завершаю работу")
                break
            except Exception as e:
                print(f"Ошибка: {e}")
                self.speak("Произошла ошибка")


def main():
    """Главная функция"""
    print("=== Голосовой помощник для Windows 10 ===")
    print("Для завершения работы нажмите Ctrl+C или скажите 'выход'")
    print("=" * 50)
    
    try:
        controller = VoiceController()
        controller.run()
    except Exception as e:
        print(f"Критическая ошибка: {e}")
        print("Убедитесь, что микрофон подключен и все зависимости установлены")


if __name__ == "__main__":
    main()
