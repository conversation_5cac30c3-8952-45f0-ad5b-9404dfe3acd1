# Голосовой помощник для Windows 10

Приложение для голосового управления компьютером на Windows 10. Использует распознавание речи для выполнения различных системных команд.

## Возможности

- 🎤 Распознавание русской речи
- 🔊 Синтез речи (ответы голосом)
- 🌐 Открытие браузера и веб-сайтов
- 🎵 Запуск музыкальных приложений
- 🧮 Открытие системных приложений (калькулятор, блокнот, проводник)
- ⏰ Сообщение текущего времени
- 🔄 Перезагрузка и выключение компьютера
- 🔊 Управление громкостью (требует nircmd)

## Установка

### 1. Установка Python зависимостей

```bash
pip install -r requirements.txt
```

Или установка по отдельности:

```bash
pip install speech_recognition==3.10.0
pip install pyttsx3==2.90
pip install pyaudio==0.2.11
```

### 2. Установка PyAudio (если возникают проблемы)

Если установка PyAudio через pip не работает, попробуйте:

**Вариант 1: Через conda**
```bash
conda install pyaudio
```

**Вариант 2: Скачать готовый wheel файл**
1. Перейдите на https://www.lfd.uci.edu/~gohlke/pythonlibs/#pyaudio
2. Скачайте подходящий .whl файл для вашей версии Python
3. Установите: `pip install путь_к_файлу.whl`

### 3. Дополнительные утилиты (опционально)

Для управления громкостью установите nircmd:
1. Скачайте с https://www.nirsoft.net/utils/nircmd.html
2. Распакуйте nircmd.exe в папку, которая есть в PATH (например, C:\Windows\System32)

## Использование

1. Запустите приложение:
```bash
python voice_control.py
```

2. Дождитесь сообщения "Голосовой помощник готов к работе!"

3. Произнесите одну из команд:

### Доступные команды

| Команда | Описание |
|---------|----------|
| "браузер", "интернет", "гугл" | Открыть браузер с Google |
| "ютуб", "youtube" | Открыть YouTube |
| "музыка", "плеер", "spotify" | Открыть музыкальный плеер |
| "калькулятор" | Открыть калькулятор |
| "блокнот" | Открыть блокнот |
| "проводник", "папки" | Открыть проводник |
| "время", "сколько времени" | Сказать текущее время |
| "выключи компьютер" | Выключить компьютер (с задержкой 30 сек) |
| "перезагрузка" | Перезагрузить компьютер (с задержкой 30 сек) |
| "отмена" | Отменить выключение/перезагрузку |
| "громкость больше/меньше" | Изменить громкость |
| "помощь" | Показать список команд |
| "выход", "пока" | Завершить работу |

## Устранение неполадок

### Проблемы с микрофоном
- Убедитесь, что микрофон подключен и работает
- Проверьте настройки конфиденциальности Windows (Параметры → Конфиденциальность → Микрофон)
- Попробуйте запустить от имени администратора

### Проблемы с распознаванием речи
- Говорите четко и не слишком быстро
- Убедитесь в наличии интернет-соединения (используется Google Speech Recognition)
- Проверьте, что микрофон не заглушен

### Проблемы с синтезом речи
- Убедитесь, что в системе установлены русские голоса
- Проверьте настройки звука в Windows

## Требования

- Windows 10
- Python 3.7+
- Рабочий микрофон
- Интернет-соединение (для распознавания речи)
- Права администратора (для некоторых команд)

## Лицензия

MIT License
