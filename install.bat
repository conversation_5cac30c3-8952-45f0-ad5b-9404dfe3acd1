@echo off
echo ========================================
echo Установка голосового помощника
echo ========================================
echo.

echo Проверка Python...
python --version
if %errorlevel% neq 0 (
    echo ОШИБКА: Python не найден!
    echo Установите Python с https://python.org
    pause
    exit /b 1
)

echo.
echo Установка зависимостей...
pip install --upgrade pip
pip install -r requirements.txt

if %errorlevel% neq 0 (
    echo.
    echo ОШИБКА при установке зависимостей!
    echo Попробуйте установить вручную:
    echo pip install speech_recognition pyttsx3 pyaudio
    pause
    exit /b 1
)

echo.
echo ========================================
echo Установка завершена успешно!
echo ========================================
echo.
echo Для запуска используйте: python voice_control.py
echo.
pause
